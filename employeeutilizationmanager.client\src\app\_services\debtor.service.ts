import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import { Observable } from 'rxjs';

import { Debtor } from '../_models';

@Injectable({
    providedIn: 'root'
})
export class DebtorService {

    constructor(
        private _http: HttpClient) {
    }

    getAll(): Observable<Debtor[]> {
        return this._http
            .get<Debtor[]>(
                '/api/debtor/list');
    }

}
