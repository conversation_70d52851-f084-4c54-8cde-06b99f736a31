import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { LoginComponent } from './auth/login/login.component';
import { HomeComponent } from './pages/home/<USER>';
import { CaptureComponent } from './pages/capture/capture.component';
import { NotFoundComponent } from './pages/not-found/not-found.component';

import { AuthGuard } from './auth/auth.guard';
import { RoleGuard } from './auth/role.guard';
import { RoleType } from './core/models/role-type';
import { LayoutComponent } from './_components/layout/layout.component';

const routes: Routes = [
  {
    path: 'login',
    component: LoginComponent
  },
  {
    path: 'home',
    component: HomeComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: {
      expectedRoles: [
        RoleType.OmniGlobalAccess,
        RoleType.OMNI,
        RoleType.BasicUser,
        RoleType.BasicUserViewInvoice,
        RoleType.Finance,
        RoleType.FinanceViewDashboardFinancials,
        RoleType.Reporting,
        RoleType.ReportingViewReportComponent,
        // Add any additional roles as needed
      ],
    },
  },
  {
    path: 'capture',
    component: CaptureComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: {
      expectedRoles: [
        RoleType.OmniGlobalAccess,
        RoleType.OMNI,
        RoleType.InvoiceManagement,
        RoleType.InvoiceManagementUploadInvoice,
        RoleType.InvoiceManagementViewInvoice,
        RoleType.InvoiceManagementDeleteInvoice,
        // Add any additional roles for capture access
      ],
    },
  },
  {
    path: 'user',
    loadChildren: () =>
      import('./features/user/user.routing').then((m) => m.UserRoutes)
  },
  {
    path: '',
    // canActivate: [AuthGuard], // Typically applied to child routes or specific routes needing auth
    component: LayoutComponent,
    children: [
      {
        path: 'report',
        loadChildren: () => import('./features/reporting/report-routing.module').then((m) => m.ReportingRoutingModule),
        // canActivate: [AuthGuard] // Apply AuthGuard here if needed
      },
      {
        path: 'hr',
        loadChildren: () => import('./features/human-resources/human-resources-routing.module').then((m) => m.HumanResourcesRoutingModule),
        // canActivate: [AuthGuard]
      },
      {
        path: 'employee-updates',
        loadChildren: () => import('./features/employee-updates/employee-updates.routing').then((m) => m.EmployeeUpdatesRouting),
        // canActivate: [AuthGuard]
      },
      {
        path: 'home',
        loadChildren: () => import('./home/<USER>').then(m => m.HomeModule), // Assuming home is a module
        // canActivate: [AuthGuard]
      },
      {
        path: 'invoice',
        loadChildren: () => import('./features/invoices/invoice-routing.module').then((m) => m.InvoiceRoutingModule),
        // canActivate: [AuthGuard]
      },
      {
        path: 'report-scheduling',
        loadChildren: () => import('./features/report-schedule/report-schedule-routing.module').then((m) => m.ReportSchedulingRoutingModule),
        // canActivate: [AuthGuard]
      },
      // {
      //   path: 'test', // Ensure ReportingComponent is standalone if using loadComponent
      //   loadComponent: () => import('./features/reporting/components/reporting/reporting.component').then((c) => c.ReportingComponent),
      //   // canActivate: [AuthGuard]
      // },
      {
        path: 'settings', // Add settings route
        loadChildren: () => import('./settings/settings-routing.module').then(m => m.SettingsRoutingModule),
        // canActivate: [AuthGuard]
      },
      {
        path: '',
        redirectTo: '/home',
        pathMatch: 'full'
      },
      {
        path: '**',
        component: NotFoundComponent
      }
    ]
  },
   { path: '**', redirectTo: 'user/login' } 
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule { }
