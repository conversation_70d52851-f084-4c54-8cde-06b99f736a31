// src/app/auth/auth.interceptor.ts
import { Injectable } from '@angular/core';
import {
  HttpRequest,
  <PERSON>ttpHandler,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from './auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private baseUrl = '/api';

  constructor(private authService: AuthService) {}

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttpHandler): Observable<HttpEvent<unknown>> {
    const token = this.authService.getToken();
    // const isApiUrl = request.url.startsWith(this.baseUrl); // todo check 
    const isApiUrl = request.url.includes(this.baseUrl); // todo check 

    if (token && isApiUrl) {
      request = request.clone({
        setHeaders: { Authorization: `Bearer ${token}` }
      });
    }
    return next.handle(request);
  }
}