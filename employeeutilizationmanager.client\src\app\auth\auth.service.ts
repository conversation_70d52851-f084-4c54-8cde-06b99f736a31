// src/app/auth/auth.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { jwtDecode } from 'jwt-decode'; // Corrected import

import { User, DecodedToken } from '../core/models/user.model';
import { GroupType } from '../core/models/group-type.enum';
import { RoleType } from '../core/models/role-type'; // For deriveGroupType

const TOKEN_KEY = 'auth_token';

interface AuthResponse {
  accessToken: string;
  expiresAt: string; // Assuming ISO string date
  username: string;
  displayName: string;
  roles: string[];
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private baseUrl = '/api';
  private currentUserSubject: BehaviorSubject<User | null>;
  public currentUser: Observable<User | null>;
  private tokenExpirationTimer: any;

  constructor(private http: HttpClient, private router: Router) {
    this.currentUserSubject = new BehaviorSubject<User | null>(this.loadUserFromStorage());
    this.currentUser = this.currentUserSubject.asObservable();
  }

  public get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  private loadUserFromStorage(): User | null {
    const token = localStorage.getItem(TOKEN_KEY);
    if (token) {
      return this.decodeAndSetUser(token);
    }
    return null;
  }

  login(credentials: { username: string; password: string }): Observable<User> {
    return this.http.post<AuthResponse>(`${this.baseUrl}/auth/login`, credentials)
      .pipe(
        map((response: AuthResponse) => {
          localStorage.setItem(TOKEN_KEY, response.accessToken);
          const user = this.decodeAndSetUser(response.accessToken, response);
          if (user) {
            this.currentUserSubject.next(user);
            return user;
          }
          throw new Error('Failed to decode token or create user object.');
        }),
        catchError(this.handleError)
      );
  }

  logout(): void {
    localStorage.removeItem(TOKEN_KEY);
    if (this.tokenExpirationTimer) {
      clearTimeout(this.tokenExpirationTimer);
    }
    this.currentUserSubject.next(null);
    this.router.navigate(['/login']);
  }

  getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY);
  }

  isAuthenticated(): boolean {
    const user = this.currentUserValue;
    return !!user && !!this.getToken(); // Check both user object and token presence
  }

  private decodeAndSetUser(token: string, authResponse?: AuthResponse): User | null {
    try {
      const decodedToken = jwtDecode<DecodedToken>(token);
      const roles = Array.isArray(decodedToken.role) ? decodedToken.role : [decodedToken.role].filter(r => r);

      const user: User = {
        username: authResponse?.username || decodedToken.sub,
        displayName: authResponse?.displayName || decodedToken.name,
        roles: authResponse?.roles || roles,
        groupType: this.deriveGroupType(authResponse?.roles || roles),
        token: token
      };

      const expiresIn = (decodedToken.exp * 1000) - Date.now();
      if (expiresIn <= 0) {
        this.logout(); // Token expired
        return null;
      }
      this.autoLogout(expiresIn);
      return user;
    } catch (error) {
      console.error('Error decoding token:', error);
      this.logout(); // Invalid token
      return null;
    }
  }

  private autoLogout(expirationDuration: number): void {
    if (this.tokenExpirationTimer) {
      clearTimeout(this.tokenExpirationTimer);
    }
    this.tokenExpirationTimer = setTimeout(() => {
      this.logout();
    }, expirationDuration);
  }

  deriveGroupType(roles: string[]): GroupType {
    if (
      roles.includes(RoleType.OmniGlobalAccess) ||
      roles.includes(RoleType.OMNI)
    ) {
      return GroupType.OMNI;
    }

    if (roles.includes(RoleType.Admin) || roles.includes(RoleType.AdminUserManagement)) {
      return GroupType.ADMIN;
    }

    if (roles.includes(RoleType.Developer) || roles.includes(RoleType.DeveloperAccessRoutes)) {
      return GroupType.DEVELOPER;
    }

    if (
      roles.includes(RoleType.Finance) ||
      roles.includes(RoleType.FinanceViewDashboardFinancials) ||
      roles.includes(RoleType.FinanceViewReportFinancials)
    ) {
      return GroupType.FINANCE;
    }

    if (
      roles.includes(RoleType.HR) ||
      roles.some(r => r.startsWith('HR.'))
    ) {
      return GroupType.HR;
    }

    if (
      roles.includes(RoleType.Reporting) ||
      roles.some(r => r.startsWith('Reporting.'))
    ) {
      return GroupType.REPORTING;
    }

    if (
      roles.includes(RoleType.InvoiceManagement) ||
      roles.some(r => r.startsWith('InvoiceManagement.'))
    ) {
      return GroupType.INVOICE_MANAGEMENT;
    }

    if (
      roles.includes(RoleType.BasicUser) ||
      roles.includes(RoleType.BasicUserViewInvoice)
    ) {
      return GroupType.BASIC_USER;
    }

    if (
      roles.includes(RoleType.Training) ||
      roles.includes(RoleType.TrainingAccessRoutes)
    ) {
      return GroupType.TRAINING;
    }

    return GroupType.UNKNOWN;
  }

  hasRole(role: string): boolean {
    return this.currentUserValue?.roles.includes(role) ?? false;
  }

  hasAnyRole(roles: string[]): boolean {
    return roles.some(role => this.hasRole(role));
  }

  private handleError(error: HttpErrorResponse) {
    console.error('AuthService Error:', error);
    let errorMessage = 'An unknown error occurred!';
    if (error.error instanceof ErrorEvent) {
      // Client-side errors
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side errors
      errorMessage = error.error?.message || error.statusText || `Server error: ${error.status}`;
    }
    return throwError(() => new Error(errorMessage));
  }
}