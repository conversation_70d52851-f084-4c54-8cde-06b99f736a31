// src/app/auth/login/login.component.ts
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { first } from 'rxjs/operators';
import { AuthService } from '../auth.service';
import { PermissionService } from '../../core/services/permission.service'; // For post-login redirection logic

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css'],
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  loading = false;
  submitted = false;
  errorMessage?: string;
  returnUrl: string = '/home'; // Default redirect

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private permissionService: PermissionService // Optional: for role-based redirection
  ) {
    // Redirect to home if already logged in
    if (this.authService.currentUserValue) {
      this.router.navigate(['/home']);
    }
  }

  ngOnInit(): void {
    this.loginForm = this.formBuilder.group({
      username: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required],
    });

    // Get return url from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/home';
  }

  // Convenience getter for easy access to form fields
  get f() {
    return this.loginForm.controls;
  }

  onSubmit(): void {
    this.submitted = true;
    this.errorMessage = undefined;

    if (this.loginForm.invalid) {
      return;
    }

    this.loading = true;
    this.authService.login({ username: this.f['username'].value, password: this.f['password'].value })
      .pipe(first()) // take the first emission and complete
      .subscribe({
        next: (user) => {
          // Login successful, redirect to returnUrl or role-based dashboard
          // Example: if (this.permissionService.isOmniUser()) this.router.navigate(['/admin-dashboard']);
          this.router.navigate([this.returnUrl]);
        },
        error: (error) => {
          this.errorMessage = error.message || 'Login failed. Please check your credentials.';
          this.loading = false;
        },
      });
  }
}