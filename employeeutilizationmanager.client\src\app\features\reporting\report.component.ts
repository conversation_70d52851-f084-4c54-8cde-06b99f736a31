import { CommonModule, <PERSON><PERSON><PERSON>cyPipe, PercentPipe, DatePipe } from '@angular/common';
import { AfterViewInit, Component, OnInit, ViewChild, OnDestroy } from '@angular/core'; // Added OnDestroy
import { FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatAccordion, MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule, MatListOption, MatSelectionList } from '@angular/material/list';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { forkJoin, Subject, takeUntil } from 'rxjs'; // Import Subject, takeUntil
import { Department } from '../human-resources/models/department.model';
import { Employee } from '../human-resources/models/employee.model';
import { DepartmentService } from '../human-resources/services/department.service';
import { EmployeeUtilizationReportItemDto } from './models/employee-utilization-report-item';
import { ReportRequest } from './models/report-request.model';
import { BASIC_COLUMNS, CURRENT_COLUMNS, DISPLAYED_COLUMNS, MONTHLY_COLUMNS, MONTH_MAP, QUARTERLY_COLUMNS, YTD_COLUMNS, BILL_RATE_COLUMNS } from './report-constants'; // Import BILL_RATE_COLUMNS
import { ReportService } from './report.service';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { EmploymentType } from '../human-resources/models/employmentType';
// Removed EmployeeService import if not directly used
import { TeamLead } from '../human-resources/models/team-lead.model';
import { Supervisor } from '../human-resources/models/supervisor.model';
import { TeamLeadService } from '../human-resources/services/team-lead.service';
import { SupervisorService } from '../human-resources/services/supervisor.service';
import { HttpErrorResponse } from '@angular/common/http'; // Import HttpErrorResponse

@Component({
    selector: 'app-report', // Renamed selector to avoid conflict
    standalone: true,
    templateUrl: './report.component.html',
    styleUrls: ['./report.component.css'], // Corrected property name
    providers: [provideNativeDateAdapter()],
    imports: [
        CommonModule,
        MatDatepickerModule,
        MatSlideToggleModule,
        MatSelectModule,
        MatButtonModule,
        MatTableModule,
        MatFormFieldModule,
        MatInputModule,
        MatCardModule,
        ReactiveFormsModule,
        MatPaginatorModule,
        MatSortModule,
        MatCheckboxModule,
        MatGridListModule,
        MatAutocompleteModule,
        MatChipsModule,
        MatExpansionModule,
        MatIconModule,
        MatListModule,
        MatSelectionList,
        ScrollingModule,
        CurrencyPipe,
        PercentPipe,
        DatePipe
    ]
})
export class ReportComponent implements OnInit, AfterViewInit, OnDestroy { // Implemented OnDestroy
  private destroy$ = new Subject<void>(); // For unsubscribing
  @ViewChild(MatAccordion) accordion!: MatAccordion;
  @ViewChild('supervisorList') supervisorList!: MatSelectionList;
  @ViewChild('teamLeadList') teamLeadList!: MatSelectionList;
  @ViewChild('departmentList') departmentList!: MatSelectionList;
  @ViewChild('employmentTypeList') employmentTypeList!: MatSelectionList;

  departments: Department[] = [];
  supervisors: Supervisor[] = [];
  teamLeads: TeamLead[] = [];
  employmentTypes: EmploymentType[] = [
    { id: 1, name: 'Full-time' },
    { id: 2, name: 'Part-time' },
    { id: 3, name: 'Staffing Agency' },
    { id: 4, name: 'Contractor' },
    { id: 5, name: 'Former Employee' }
  ];

  selectedSupervisors: Supervisor[] = [];
  selectedTeamLeads: TeamLead[] = [];
  selectedDepartments: Department[] = [];
  selectedEmploymentTypes: EmploymentType[] = [];

  displayedColumns = DISPLAYED_COLUMNS;

  filterForm!: FormGroup;

  reportData: EmployeeUtilizationReportItemDto[] = [];
  dataSource = new MatTableDataSource<EmployeeUtilizationReportItemDto>(this.reportData);

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private reportService: ReportService,
    private departmentService: DepartmentService,
    private teamLeadService: TeamLeadService,
    private supervisorService: SupervisorService,
    private fb: FormBuilder) {
      this.filterForm = this.fb.group({
        endDate: new FormControl(this.getLastDateOfPreviousMonth()),
        includeFutureMonths: new FormControl(false),
        includeFormerEmployeeSection: new FormControl(false),
        moveCurrentMonthToFront: new FormControl(true),
        departments: this.fb.array([]),
        supervisors: this.fb.array([]),
        teamLeads: this.fb.array([]),
        employmentTypes: this.fb.array(this.employmentTypes.map(() => new FormControl(true))),
        showYTDValues: new FormControl(true),
        showQuarterlyValues: new FormControl(true),
        showCurrentValues: new FormControl(true),
        showMonthlyValues: new FormControl(true),
        showFutureMonths: new FormControl(false)
      });
    }

  ngOnInit() {
    this.loadData();
    this.filterForm.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => this.updateLayout());
    // Initial updateLayout called in loadData callback
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    // Sorting handled client-side by MatSort
  }

  ngOnDestroy(): void { // Added ngOnDestroy
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadData() {
    forkJoin({
      departments: this.departmentService.getDepartments(),
      teamLeads: this.teamLeadService.getTeamLeads(),
      supervisors: this.supervisorService.getSupervisors()
    }).pipe(takeUntil(this.destroy$)).subscribe(({ departments, teamLeads, supervisors }) => {
      this.departments = departments || [];
      this.teamLeads = this.getUniquePeople(teamLeads || []) as TeamLead[]; // Cast needed
      this.supervisors = this.getUniquePeople(supervisors || []) as Supervisor[]; // Cast needed

      this.createOrUpdateFormArrays();

      this.selectAllSupervisors();
      this.selectAllTeamLeads();
      this.selectAllDepartments();
      this.selectAllEmploymentTypes();

      this.updateLayout();
      this.fetchReportData();
    }, (error: HttpErrorResponse) => { // Added type for error
        console.error("Error loading initial filter data:", error);
    });
  }

  getUniquePeople<T extends { firstName: string; lastName: string }>(
    people: T[] | null
  ): T[] {
    if (!people) return [];
    const uniqueMap = new Map<string, T>();
    for (const person of people) {
       if (person && person.firstName && person.lastName) {
            const fullName = `${person.firstName} ${person.lastName}`;
            if (!uniqueMap.has(fullName)) {
                uniqueMap.set(fullName, person);
            }
       }
    }
    return Array.from(uniqueMap.values());
  }


    createOrUpdateFormArrays() {
        const departmentControls = this.departments.map(() => new FormControl(true));
        this.filterForm.setControl('departments', this.fb.array(departmentControls));

        const supervisorControls = this.supervisors.map(() => new FormControl(true));
        this.filterForm.setControl('supervisors', this.fb.array(supervisorControls));

        const teamLeadControls = this.teamLeads.map(() => new FormControl(true));
        this.filterForm.setControl('teamLeads', this.fb.array(teamLeadControls));

        const employmentTypeControls = this.employmentTypes.map(() => new FormControl(true));
        this.filterForm.setControl('employmentTypes', this.fb.array(employmentTypeControls));
    }

    getLastDateOfPreviousMonth(): Date {
      const now = new Date();
      return new Date(now.getFullYear(), now.getMonth(), 0);
    }

    onToggleChange() {
      this.updateLayout();
    }

    applyFilter(event: Event) {
      const filterValue = (event.target as HTMLInputElement).value;
      this.dataSource.filter = filterValue.trim().toLowerCase();
      if (this.dataSource.paginator) {
          this.dataSource.paginator.firstPage();
      }
    }

    fetchReportData() {
        if (!this.filterForm) return; // Guard against early calls

      const filters: ReportRequest = {
        endDate: (this.filterForm.get('endDate')!.value?.toISOString()) || new Date().toISOString(),
        includeFormerEmployeeSection: this.filterForm.get('includeFormerEmployeeSection')!.value ?? false,
        includeFutureMonths: this.filterForm.get('includeFutureMonths')!.value ?? false,
        moveCurrentMonthToFront: this.filterForm.get('moveCurrentMonthToFront')!.value ?? false,
        departments: this.selectedDepartments.map(d => d.departmentName),
        supervisors: this.selectedSupervisors.map(s => `${s.firstName} ${s.lastName}`), // Send name
        employmentTypes: this.selectedEmploymentTypes.map(et => et.name),
      };

      this.reportService.getReport(filters).pipe(takeUntil(this.destroy$)).subscribe({
        next: data => {
            this.reportData = data || [];
            this.dataSource.data = this.reportData;
            // Paginator & Sort are handled by Angular Material bindings now
        },
        error: (error: HttpErrorResponse) => { // Added type for error
            console.error("Error fetching report data:", error);
            this.reportData = [];
            this.dataSource.data = [];
        }
     });
    }

    updateLayout() {
        if (!this.filterForm) return;

        let showColumns = [...BASIC_COLUMNS];

        if (this.filterForm.get('showCurrentValues')?.value) {
            showColumns = [...showColumns, ...CURRENT_COLUMNS];
        }
        if (this.filterForm.get('showYTDValues')?.value) {
             showColumns = [...showColumns, ...YTD_COLUMNS];
        }
        if (this.filterForm.get('showQuarterlyValues')?.value) {
             showColumns = [...showColumns, ...QUARTERLY_COLUMNS];
        }
        if (this.filterForm.get('showMonthlyValues')?.value) {
             showColumns = [...showColumns, ...MONTHLY_COLUMNS];
        }
        showColumns = [...showColumns, ...BILL_RATE_COLUMNS]; // BILL_RATE_COLUMNS is now imported

        const includeFutureMonths = this.filterForm.get('showFutureMonths')?.value;
        const endDate = this.filterForm.get('endDate')?.value || new Date();
        const currentMonth = endDate.getMonth();

        this.displayedColumns = showColumns.filter(column => {
            if (column.startsWith('q')) return true;

            const monthPrefix = column.substring(0, 3).toLowerCase();
            const monthIndex = Object.values(MONTH_MAP).indexOf(monthPrefix);

            if (monthIndex === -1) return true;

            return includeFutureMonths || monthIndex <= currentMonth;
        });
    }


    toggleAll(list: MatSelectionList | undefined, formArrayName: string, updateFn: () => void, select: boolean) { // Added undefined check for list
        if (!list) return;
        const formArray = this.filterForm.get(formArrayName) as FormArray;
        formArray.controls.forEach(control => control.setValue(select, { emitEvent: false }));
        list.options.forEach(option => option._setSelected(select));
        updateFn.call(this);
    }

    selectAllSupervisors() { this.toggleAll(this.supervisorList, 'supervisors', this.updateSelectedSupervisors, true); }
    deselectAllSupervisors() { this.toggleAll(this.supervisorList, 'supervisors', this.updateSelectedSupervisors, false); }

    selectAllTeamLeads() { this.toggleAll(this.teamLeadList, 'teamLeads', this.updateSelectedTeamLeads, true); }
    deselectAllTeamLeads() { this.toggleAll(this.teamLeadList, 'teamLeads', this.updateSelectedTeamLeads, false); }

    selectAllDepartments() { this.toggleAll(this.departmentList, 'departments', this.updateSelectedDepartments, true); }
    deselectAllDepartments() { this.toggleAll(this.departmentList, 'departments', this.updateSelectedDepartments, false); }

    selectAllEmploymentTypes() { this.toggleAll(this.employmentTypeList, 'employmentTypes', this.updateSelectedEmploymentTypes, true); }
    deselectAllEmploymentTypes() { this.toggleAll(this.employmentTypeList, 'employmentTypes', this.updateSelectedEmploymentTypes, false); }


    updateSelectedSupervisors() {
        if (!this.supervisorList) return;
        this.selectedSupervisors = this.supervisorList.selectedOptions.selected.map(option => option.value);
        const formArray = this.filterForm.get('supervisors') as FormArray;
        this.supervisors.forEach((item, index) => {
            const isSelected = this.selectedSupervisors.some(s => s.supervisorId === item.supervisorId);
            if(formArray.at(index)) { // Check if control exists
                 formArray.at(index).setValue(isSelected, { emitEvent: false });
            }
        });
    }

    updateSelectedDepartments() {
        if (!this.departmentList) return;
        this.selectedDepartments = this.departmentList.selectedOptions.selected.map(option => option.value);
        const formArray = this.filterForm.get('departments') as FormArray;
        this.departments.forEach((item, index) => {
            const isSelected = this.selectedDepartments.some(d => d.departmentId === item.departmentId);
             if(formArray.at(index)) {
                formArray.at(index).setValue(isSelected, { emitEvent: false });
             }
        });
    }

    updateSelectedEmploymentTypes() {
        if (!this.employmentTypeList) return;
        this.selectedEmploymentTypes = this.employmentTypeList.selectedOptions.selected.map(option => option.value);
         const formArray = this.filterForm.get('employmentTypes') as FormArray;
         this.employmentTypes.forEach((item, index) => {
             const isSelected = this.selectedEmploymentTypes.some(e => e.id === item.id);
              if(formArray.at(index)) {
                formArray.at(index).setValue(isSelected, { emitEvent: false });
              }
         });
    }

    updateSelectedTeamLeads() {
        if (!this.teamLeadList) return;
        this.selectedTeamLeads = this.teamLeadList.selectedOptions.selected.map(option => option.value);
         const formArray = this.filterForm.get('teamLeads') as FormArray;
         this.teamLeads.forEach((item, index) => {
             const isSelected = this.selectedTeamLeads.some(tl => tl.id === item.id);
              if(formArray.at(index)) {
                formArray.at(index).setValue(isSelected, { emitEvent: false });
              }
         });
    }

    formatSupervisors(supervisors: Supervisor[] | null): string {
        if (!supervisors || supervisors.length === 0) return 'None'; // Provide default text
        return supervisors.map(s => `${s.firstName} ${s.lastName}`).join(', ');
    }

    formatTeamLeads(teamLeads: TeamLead[] | null): string {
        if (!teamLeads || teamLeads.length === 0) return 'None';
        return teamLeads.map(lead => `${lead.firstName} ${lead.lastName}`).join(', ');
    }

    formatDepartments(departments: Department[] | null): string {
        if (!departments || departments.length === 0) return 'None';
        return departments.map(dept => dept.departmentName).join(', ');
    }

    formatEmployeeTypes(employeeTypes: EmploymentType[] | null): string {
        if (!employeeTypes || employeeTypes.length === 0) return 'None';
        return employeeTypes.map(et => et.name).join(', ');
    }


     exportReport() {
      const endDate = this.filterForm.get('endDate')?.value || new Date();
      const includeFormerEmployeeSection = this.filterForm.get('includeFormerEmployeeSection')?.value ?? false;
      const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
      const monthName = monthNames[endDate.getMonth()];

      const now = new Date();
      const utcMonth = String(now.getUTCMonth() + 1).padStart(2, '0');
      const utcDay = String(now.getUTCDate()).padStart(2, '0');
      const utcHours = String(now.getUTCHours()).padStart(2, '0');
      const utcMinutes = String(now.getUTCMinutes()).padStart(2, '0');
      const timestamp = `${utcMonth}${utcDay}${utcHours}${utcMinutes}`;

      this.reportService.exportReport(endDate, includeFormerEmployeeSection)
        .pipe(takeUntil(this.destroy$)) // Added takeUntil
        .subscribe({
          next: (blob: Blob) => { // Added type for blob
            if (blob && blob.size > 0) {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${monthName}_Employee_Utilization_Report_${timestamp}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            } else {
                console.error("Received empty or invalid blob for download.");
            }
        },
        error: (error: HttpErrorResponse) => { // Added type for error
            console.error("Error exporting report:", error);
        }
      });
    }
  }