import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { EmployeeUtilizationReportItemDto } from './models/employee-utilization-report-item';
import { ReportRequest } from './models/report-request.model';

@Injectable({
  providedIn: 'root'
})
export class ReportService {
  private baseUrl = '/api';
  private reportUrl = `${this.baseUrl}/report`;
  private reportExportUrl = `${this.reportUrl}/export`;

  constructor(private http: HttpClient) {}

  getReport(request: ReportRequest): Observable<EmployeeUtilizationReportItemDto[]> {
    return this.http.post<EmployeeUtilizationReportItemDto[]>(this.reportUrl, request);
  }

  exportReport(endDate: Date, includeFormerEmployeeSection: boolean): Observable<Blob> {
    const params = new HttpParams()
      .set('endDate', endDate.toISOString())
      .set('includeFormerEmployeeSection', includeFormerEmployeeSection.toString());

    return this.http.get(this.reportExportUrl, { params, responseType: 'blob' });
  }
}
