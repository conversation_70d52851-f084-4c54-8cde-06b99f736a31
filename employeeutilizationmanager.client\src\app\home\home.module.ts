import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';


import { HomeComponent } from './home.component';
import { HomeRouting } from './home.routing';

import { AuthenticationService, DebtorService } from '../_services';
// import { FileService } from '../common/file';
import { CustomPipesModule } from '../common/pipes';

@NgModule({
    declarations: [
        HomeComponent
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        FormsModule,
        CustomPipesModule,
        HomeRouting
    ],
    providers: [
        DebtorService,
        // FileService,
        AuthenticationService
    ]
})

export class HomeModule { }
