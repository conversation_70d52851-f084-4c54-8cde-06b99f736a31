// src/app/pages/capture/capture.component.ts
import { Component, OnInit } from '@angular/core';
import { PermissionService } from '../../core/services/permission.service';

@Component({
  selector: 'app-capture',
  templateUrl: './capture.component.html',
  styleUrls: ['./capture.component.css'],
})
export class CaptureComponent implements OnInit {
  constructor(public permissionService: PermissionService) {} // Make public for template access

  ngOnInit(): void {
    // Logic to load claim data if an ID is present in the route, etc.
  }

  // Example getter methods if more complex logic is needed than direct service access
  // get canUserAddNotes(): boolean {
  //   return this.permissionService.canAddNotes;
  // }

  // get isFormReadOnly(): boolean {
  //   return this.permissionService.isCaptureReadOnly;
  // }

  // get canUserDeleteClaim(): boolean {
  //  return this.permissionService.canDeleteClaim;
  // }
}