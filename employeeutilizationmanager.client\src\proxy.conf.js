const { env } = require('process');

const target = env.ASPNETCORE_HTTPS_PORT ? `https://localhost:${env.ASPNETCORE_HTTPS_PORT}` :
  env.ASPNETCORE_URLS ? env.ASPNETCORE_URLS.split(';')[0] : 'https://localhost:7081';

const PROXY_CONFIG = [
  {
    context: [
      "/weatherforecast",
      "/api",
      "/api/report",
      "/api/department",
      "/api/department/all",
      "/api/employee/",
      "/api/Employee/process-changes",
      "/api/employeetype/",
      "/api/fileupload",
      "/api/Auth/login",
      "/api/fileUpload/upload",
      "/api/invoice",
      "/api/invoice/upload",
      "/api/invoice/list",
      "/api/invoice/details",
      "/api/invoice/confirm",
      "/api/reportschedules",
      "/api/jobhistory",
      "/api/jobhistory/download",
      "/api/auth/login",
      "/api/auth/register",
      "/api/auth/refresh",
      "/api/users",
      "/api/users/profile",
      "/api/auth",
      "/api/auth/*",
      "/swagger"
    ],
    target,
    secure: false
  }
]

module.exports = PROXY_CONFIG;
